/**
 * 批次11：AI与计算机视觉节点注册表测试
 * 测试40个AI与计算机视觉节点的注册功能
 */

import { UnregisteredAINodesRegistry, UNREGISTERED_AI_NODE_TYPES } from '../UnregisteredAINodesRegistry';
import { NodeRegistry } from '../NodeRegistry';

describe('UnregisteredAINodesRegistry', () => {
  let registry: UnregisteredAINodesRegistry;

  beforeEach(() => {
    registry = UnregisteredAINodesRegistry.getInstance();
    registry.resetRegistration();
  });

  afterEach(() => {
    registry.resetRegistration();
  });

  describe('基础功能测试', () => {
    test('应该能够获取单例实例', () => {
      const instance1 = UnregisteredAINodesRegistry.getInstance();
      const instance2 = UnregisteredAINodesRegistry.getInstance();
      expect(instance1).toBe(instance2);
    });

    test('初始状态应该未注册', () => {
      expect(registry.isRegistered()).toBe(false);
    });

    test('应该能够重置注册状态', () => {
      registry.registerAllNodes();
      expect(registry.isRegistered()).toBe(true);
      
      registry.resetRegistration();
      expect(registry.isRegistered()).toBe(false);
    });
  });

  describe('节点注册测试', () => {
    test('应该能够注册所有40个AI与计算机视觉节点', () => {
      registry.registerAllNodes();
      expect(registry.isRegistered()).toBe(true);
      
      const allNodeTypes = registry.getAllRegisteredNodeTypes();
      expect(allNodeTypes).toHaveLength(40);
    });

    test('应该能够防止重复注册', () => {
      registry.registerAllNodes();
      const firstRegistration = registry.isRegistered();
      
      registry.registerAllNodes(); // 尝试重复注册
      const secondRegistration = registry.isRegistered();
      
      expect(firstRegistration).toBe(true);
      expect(secondRegistration).toBe(true);
    });

    test('应该正确注册模型管理节点', () => {
      registry.registerAllNodes();
      
      const modelManagementNodes = [
        'ai/modelDeployment',
        'ai/modelMonitoring',
        'ai/modelVersioning',
        'ai/autoML',
        'ai/explainableAI',
        'ai/aiEthics',
        'ai/modelCompression',
        'ai/quantization',
        'ai/pruning',
        'ai/distillation'
      ];

      modelManagementNodes.forEach(nodeType => {
        expect(registry.hasNodeType(nodeType)).toBe(true);
      });
    });

    test('应该正确注册计算机视觉节点', () => {
      registry.registerAllNodes();
      
      const cvNodes = [
        'cv/imageSegmentation',
        'cv/objectTracking',
        'cv/faceRecognition',
        'cv/opticalCharacterRecognition',
        'cv/imageGeneration',
        'cv/styleTransfer',
        'cv/imageEnhancement',
        'cv/augmentedReality'
      ];

      cvNodes.forEach(nodeType => {
        expect(registry.hasNodeType(nodeType)).toBe(true);
      });
    });

    test('应该正确注册深度学习节点', () => {
      registry.registerAllNodes();
      
      const dlNodes = [
        'dl/transformerModel',
        'dl/ganModel',
        'dl/vaeModel',
        'dl/attentionMechanism',
        'dl/embeddingLayer',
        'dl/dropoutLayer',
        'dl/batchNormalization',
        'dl/activationFunction',
        'dl/lossFunction',
        'dl/optimizer',
        'dl/regularization',
        'dl/transferLearning'
      ];

      dlNodes.forEach(nodeType => {
        expect(registry.hasNodeType(nodeType)).toBe(true);
      });
    });
  });

  describe('节点分类测试', () => {
    beforeEach(() => {
      registry.registerAllNodes();
    });

    test('应该正确设置节点分类', () => {
      const categories = registry.getNodeCategories();
      expect(categories.size).toBe(5);
      
      expect(categories.has('模型管理')).toBe(true);
      expect(categories.has('AutoML')).toBe(true);
      expect(categories.has('模型优化')).toBe(true);
      expect(categories.has('计算机视觉')).toBe(true);
      expect(categories.has('深度学习')).toBe(true);
    });

    test('应该能够按分类获取节点', () => {
      const modelManagementNodes = registry.getNodesByCategory('模型管理');
      expect(modelManagementNodes).toHaveLength(3);
      expect(modelManagementNodes).toContain('ai/modelDeployment');
      expect(modelManagementNodes).toContain('ai/modelMonitoring');
      expect(modelManagementNodes).toContain('ai/modelVersioning');

      const cvNodes = registry.getNodesByCategory('计算机视觉');
      expect(cvNodes).toHaveLength(8);
      expect(cvNodes).toContain('cv/imageSegmentation');
      expect(cvNodes).toContain('cv/faceRecognition');

      const dlNodes = registry.getNodesByCategory('深度学习');
      expect(dlNodes).toHaveLength(12);
      expect(dlNodes).toContain('dl/transformerModel');
      expect(dlNodes).toContain('dl/ganModel');
    });

    test('应该能够处理不存在的分类', () => {
      const nonExistentNodes = registry.getNodesByCategory('不存在的分类');
      expect(nonExistentNodes).toHaveLength(0);
    });
  });

  describe('节点实例化测试', () => {
    beforeEach(() => {
      registry.registerAllNodes();
    });

    test('应该能够创建节点实例', () => {
      const modelDeploymentNode = registry.createNode('ai/modelDeployment');
      expect(modelDeploymentNode).toBeDefined();
      expect(modelDeploymentNode.constructor.name).toBe('ModelDeploymentNode');

      const imageSegmentationNode = registry.createNode('cv/imageSegmentation');
      expect(imageSegmentationNode).toBeDefined();
      expect(imageSegmentationNode.constructor.name).toBe('ImageSegmentationNode');

      const transformerNode = registry.createNode('dl/transformerModel');
      expect(transformerNode).toBeDefined();
      expect(transformerNode.constructor.name).toBe('TransformerModelNode');
    });

    test('应该在节点类型不存在时抛出错误', () => {
      expect(() => {
        registry.createNode('nonexistent/node');
      }).toThrow('未找到节点类型: nonexistent/node');
    });
  });

  describe('验证功能测试', () => {
    test('应该能够验证注册状态', () => {
      registry.registerAllNodes();
      const isValid = registry.validateRegistration();
      expect(isValid).toBe(true);
    });

    test('应该能够获取所有节点类型', () => {
      registry.registerAllNodes();
      const allTypes = registry.getAllNodeTypes();
      expect(allTypes.size).toBe(40);
    });
  });

  describe('常量测试', () => {
    test('应该正确定义节点类型常量', () => {
      expect(UNREGISTERED_AI_NODE_TYPES.MODEL_DEPLOYMENT).toBe('ai/modelDeployment');
      expect(UNREGISTERED_AI_NODE_TYPES.IMAGE_SEGMENTATION).toBe('cv/imageSegmentation');
      expect(UNREGISTERED_AI_NODE_TYPES.TRANSFORMER_MODEL).toBe('dl/transformerModel');
      expect(UNREGISTERED_AI_NODE_TYPES.GAN_MODEL).toBe('dl/ganModel');
      expect(UNREGISTERED_AI_NODE_TYPES.FACE_RECOGNITION).toBe('cv/faceRecognition');
    });

    test('应该包含所有40个节点类型常量', () => {
      const constantKeys = Object.keys(UNREGISTERED_AI_NODE_TYPES);
      expect(constantKeys).toHaveLength(40);
    });
  });

  describe('集成测试', () => {
    test('应该能够与NodeRegistry正确集成', () => {
      registry.registerAllNodes();
      
      // 验证节点是否在全局注册表中
      const allRegisteredTypes = registry.getAllRegisteredNodeTypes();
      allRegisteredTypes.forEach(nodeType => {
        expect(NodeRegistry.hasNodeType(nodeType)).toBe(true);
      });
    });

    test('应该能够通过NodeRegistry创建节点实例', () => {
      registry.registerAllNodes();
      
      const modelDeploymentNode = NodeRegistry.createNode('ai/modelDeployment');
      expect(modelDeploymentNode).toBeDefined();
      
      const imageSegmentationNode = NodeRegistry.createNode('cv/imageSegmentation');
      expect(imageSegmentationNode).toBeDefined();
      
      const transformerNode = NodeRegistry.createNode('dl/transformerModel');
      expect(transformerNode).toBeDefined();
    });
  });

  describe('错误处理测试', () => {
    test('应该能够处理注册过程中的错误', () => {
      // 这个测试需要模拟注册失败的情况
      // 在实际实现中，可能需要mock某些依赖来触发错误
      expect(() => {
        registry.registerAllNodes();
      }).not.toThrow();
    });

    test('应该能够处理获取不存在节点类型的情况', () => {
      registry.registerAllNodes();
      const nonExistentNode = registry.getNodeType('nonexistent/node');
      expect(nonExistentNode).toBeUndefined();
    });
  });
});
