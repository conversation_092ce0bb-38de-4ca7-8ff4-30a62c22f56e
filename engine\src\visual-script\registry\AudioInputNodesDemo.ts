/**
 * 音频与输入系统节点演示程序
 * 展示批次13音频与输入系统节点的功能和用法
 */

import { AudioInputNodesRegistry } from './AudioInputNodesRegistry';
import { NodeRegistry } from './NodeRegistry';

/**
 * 音频与输入系统节点演示类
 */
export class AudioInputNodesDemo {
  private registry: AudioInputNodesRegistry;
  private nodeRegistry: typeof NodeRegistry;

  constructor() {
    this.registry = AudioInputNodesRegistry.getInstance();
    this.nodeRegistry = NodeRegistry;
  }

  /**
   * 运行完整演示
   */
  public async runDemo(): Promise<void> {
    console.log('🎵 音频与输入系统节点演示开始');
    console.log('=====================================');

    try {
      // 注册所有节点
      this.registry.registerAllNodes();
      console.log('✅ 节点注册完成');

      // 演示基础音频功能
      await this.demoBasicAudio();

      // 演示高级音频功能
      await this.demoAdvancedAudio();

      // 演示音频系统功能
      await this.demoAudioSystem();

      // 演示音频优化功能
      await this.demoAudioOptimization();

      // 演示基础输入功能
      await this.demoBasicInput();

      // 演示高级输入功能
      await this.demoAdvancedInput();

      // 演示综合应用场景
      await this.demoIntegratedScenarios();

      console.log('=====================================');
      console.log('🎉 音频与输入系统节点演示完成');

    } catch (error) {
      console.error('❌ 演示过程中发生错误:', error);
    }
  }

  /**
   * 演示基础音频功能
   */
  private async demoBasicAudio(): Promise<void> {
    console.log('\n🔊 基础音频功能演示');
    console.log('-------------------');

    // 音频加载节点演示
    const loadAudioNode = this.nodeRegistry.createNode('audio/loadAudio');
    const loadResult = loadAudioNode.execute({
      url: 'assets/audio/background.mp3',
      preload: true,
      loop: false
    });
    console.log('音频加载结果:', loadResult);

    // 音频播放节点演示
    const playAudioNode = this.nodeRegistry.createNode('audio/playAudio');
    const playResult = playAudioNode.execute({
      audioId: loadResult.audioId,
      volume: 0.8,
      pitch: 1.0,
      loop: false
    });
    console.log('音频播放结果:', playResult);

    // 空间音频节点演示
    const spatialAudioNode = this.nodeRegistry.createNode('audio/spatialAudio');
    const spatialResult = spatialAudioNode.execute({
      audioId: loadResult.audioId,
      position: { x: 10, y: 0, z: 5 },
      maxDistance: 50,
      rolloffFactor: 1.0
    });
    console.log('空间音频结果:', spatialResult);

    // 音频监听器节点演示
    const listenerNode = this.nodeRegistry.createNode('audio/audioListener');
    const listenerResult = listenerNode.execute({
      position: { x: 0, y: 0, z: 0 },
      orientation: { x: 0, y: 0, z: -1 },
      masterVolume: 1.0
    });
    console.log('音频监听器结果:', listenerResult);
  }

  /**
   * 演示高级音频功能
   */
  private async demoAdvancedAudio(): Promise<void> {
    console.log('\n🎛️ 高级音频功能演示');
    console.log('-------------------');

    // 高级空间音频节点演示
    const advancedSpatialNode = this.nodeRegistry.createNode('audio/advancedSpatialAudio');
    const advancedSpatialResult = advancedSpatialNode.execute({
      audioSource: 'audio_source_1',
      position: { x: 5, y: 2, z: -3 },
      velocity: { x: 1, y: 0, z: 0 },
      orientation: { x: 0, y: 0, z: -1 },
      coneAngle: 45,
      coneOuterAngle: 90
    });
    console.log('高级空间音频结果:', advancedSpatialResult);

    // 音频滤波器节点演示
    const filterNode = this.nodeRegistry.createNode('audio/audioFilter');
    const filterResult = filterNode.execute({
      audioSource: 'audio_source_1',
      filterType: 'lowpass',
      frequency: 1000,
      Q: 1.0,
      gain: 0,
      enabled: true
    });
    console.log('音频滤波器结果:', filterResult);

    // 音频效果节点演示
    const effectNode = this.nodeRegistry.createNode('audio/audioEffect');
    const effectResult = effectNode.execute({
      audioSource: 'audio_source_1',
      effectType: 'reverb',
      parameters: { roomSize: 0.5, damping: 0.3 },
      wetness: 0.4,
      enabled: true
    });
    console.log('音频效果结果:', effectResult);
  }

  /**
   * 演示音频系统功能
   */
  private async demoAudioSystem(): Promise<void> {
    console.log('\n🎚️ 音频系统功能演示');
    console.log('-------------------');

    // 音频混合器节点演示
    const mixerNode = this.nodeRegistry.createNode('audio/audioMixer');
    const mixerResult = mixerNode.execute({
      audioSources: ['source1', 'source2', 'source3'],
      channelVolumes: [0.8, 0.6, 0.9],
      masterVolume: 1.0,
      crossfade: 0.0
    });
    console.log('音频混合器结果:', mixerResult);

    // 音频效果链节点演示
    const effectChainNode = this.nodeRegistry.createNode('audio/audioEffectChain');
    const effectChainResult = effectChainNode.execute({
      audioSource: 'source1',
      effectChain: ['reverb', 'delay', 'chorus'],
      bypass: false,
      wetDryMix: 0.5
    });
    console.log('音频效果链结果:', effectChainResult);

    // 音频混响节点演示
    const reverbNode = this.nodeRegistry.createNode('audio/audioReverb');
    const reverbResult = reverbNode.execute({
      audioSource: 'source1',
      roomSize: 0.7,
      damping: 0.4,
      wetLevel: 0.3,
      dryLevel: 0.7
    });
    console.log('音频混响结果:', reverbResult);

    // 音频均衡器节点演示
    const eqNode = this.nodeRegistry.createNode('audio/audioEQ');
    const eqResult = eqNode.execute({
      audioSource: 'source1',
      bands: [
        { frequency: 100, gain: 2 },
        { frequency: 1000, gain: 0 },
        { frequency: 10000, gain: -3 }
      ],
      presets: 'rock',
      enabled: true
    });
    console.log('音频均衡器结果:', eqResult);
  }

  /**
   * 演示音频优化功能
   */
  private async demoAudioOptimization(): Promise<void> {
    console.log('\n⚡ 音频优化功能演示');
    console.log('-------------------');

    // 音频优化节点演示
    const optimizationNode = this.nodeRegistry.createNode('audio/audioOptimization');
    const optimizationResult = optimizationNode.execute({
      maxSources: 32,
      compressionLevel: 0.7,
      qualityLevel: 'high',
      enableOptimization: true
    });
    console.log('音频优化结果:', optimizationResult);

    // 音频流式传输节点演示
    const streamingNode = this.nodeRegistry.createNode('audio/audioStreaming');
    const streamingResult = streamingNode.execute({
      streamUrl: 'https://example.com/audio/stream',
      bufferSize: 4096,
      preloadTime: 2.0,
      autoPlay: true
    });
    console.log('音频流式传输结果:', streamingResult);
  }

  /**
   * 演示基础输入功能
   */
  private async demoBasicInput(): Promise<void> {
    console.log('\n⌨️ 基础输入功能演示');
    console.log('-------------------');

    // 键盘输入节点演示
    const keyboardNode = this.nodeRegistry.createNode('input/keyboard');
    const keyboardResult = keyboardNode.execute({
      keyCode: 'Space'
    });
    console.log('键盘输入结果:', keyboardResult);

    // 鼠标输入节点演示
    const mouseNode = this.nodeRegistry.createNode('input/mouse');
    const mouseResult = mouseNode.execute({
      button: 'left'
    });
    console.log('鼠标输入结果:', mouseResult);

    // 触摸输入节点演示
    const touchNode = this.nodeRegistry.createNode('input/touch');
    const touchResult = touchNode.execute({
      touchIndex: 0
    });
    console.log('触摸输入结果:', touchResult);

    // 游戏手柄输入节点演示
    const gamepadNode = this.nodeRegistry.createNode('input/gamepad');
    const gamepadResult = gamepadNode.execute({
      gamepadIndex: 0,
      buttonIndex: 0,
      axisIndex: 0
    });
    console.log('游戏手柄输入结果:', gamepadResult);
  }

  /**
   * 演示高级输入功能
   */
  private async demoAdvancedInput(): Promise<void> {
    console.log('\n🎤 高级输入功能演示');
    console.log('-------------------');

    // 语音输入节点演示
    const voiceNode = this.nodeRegistry.createNode('input/voice');
    const voiceResult = voiceNode.execute({
      language: 'zh-CN',
      continuous: true,
      commands: ['开始', '停止', '暂停', '继续'],
      sensitivity: 0.8
    });
    console.log('语音输入结果:', voiceResult);
  }

  /**
   * 演示综合应用场景
   */
  private async demoIntegratedScenarios(): Promise<void> {
    console.log('\n🎮 综合应用场景演示');
    console.log('-------------------');

    // 场景1: 交互式音频游戏
    console.log('场景1: 交互式音频游戏');
    await this.demoInteractiveAudioGame();

    // 场景2: 语音控制音频播放器
    console.log('\n场景2: 语音控制音频播放器');
    await this.demoVoiceControlledPlayer();

    // 场景3: 3D空间音频体验
    console.log('\n场景3: 3D空间音频体验');
    await this.demo3DSpatialAudio();
  }

  /**
   * 交互式音频游戏场景
   */
  private async demoInteractiveAudioGame(): Promise<void> {
    // 加载游戏音效
    const loadNode = this.nodeRegistry.createNode('audio/loadAudio');
    const audioId = loadNode.execute({
      url: 'assets/audio/game_sound.mp3',
      preload: true
    }).audioId;

    // 设置键盘控制
    const keyboardNode = this.nodeRegistry.createNode('input/keyboard');
    const keyResult = keyboardNode.execute({ keyCode: 'Space' });

    // 根据输入播放音效
    if (keyResult.isPressed) {
      const playNode = this.nodeRegistry.createNode('audio/playAudio');
      const playResult = playNode.execute({
        audioId: audioId,
        volume: 0.8
      });
      console.log('游戏音效播放:', playResult);
    }
  }

  /**
   * 语音控制音频播放器场景
   */
  private async demoVoiceControlledPlayer(): Promise<void> {
    // 设置语音识别
    const voiceNode = this.nodeRegistry.createNode('input/voice');
    const voiceResult = voiceNode.execute({
      language: 'zh-CN',
      commands: ['播放', '暂停', '停止', '音量增大', '音量减小']
    });

    // 根据语音命令控制播放
    if (voiceResult.matchedCommand) {
      const command = voiceResult.matchedCommand;
      console.log('识别到语音命令:', command);

      // 执行相应的音频操作
      switch (command) {
        case '播放':
          const playNode = this.nodeRegistry.createNode('audio/playAudio');
          playNode.execute({ audioId: 'current_audio', volume: 0.8 });
          break;
        case '音量增大':
          const listenerNode = this.nodeRegistry.createNode('audio/audioListener');
          listenerNode.execute({ masterVolume: 1.2 });
          break;
      }
    }
  }

  /**
   * 3D空间音频体验场景
   */
  private async demo3DSpatialAudio(): Promise<void> {
    // 设置音频监听器
    const listenerNode = this.nodeRegistry.createNode('audio/audioListener');
    listenerNode.execute({
      position: { x: 0, y: 0, z: 0 },
      orientation: { x: 0, y: 0, z: -1 }
    });

    // 创建多个空间音频源
    const spatialNode1 = this.nodeRegistry.createNode('audio/spatialAudio');
    spatialNode1.execute({
      audioId: 'ambient_1',
      position: { x: -10, y: 0, z: 0 },
      maxDistance: 20
    });

    const spatialNode2 = this.nodeRegistry.createNode('audio/spatialAudio');
    spatialNode2.execute({
      audioId: 'ambient_2',
      position: { x: 10, y: 0, z: 0 },
      maxDistance: 20
    });

    // 使用鼠标控制监听器方向
    const mouseNode = this.nodeRegistry.createNode('input/mouse');
    const mouseResult = mouseNode.execute({});
    
    if (mouseResult.deltaPosition) {
      // 根据鼠标移动更新监听器方向
      console.log('更新监听器方向:', mouseResult.deltaPosition);
    }
  }

  /**
   * 显示注册统计信息
   */
  public showRegistrationStats(): void {
    const stats = this.registry.getRegistrationStats();
    console.log('\n📊 注册统计信息');
    console.log('================');
    console.log(`总节点数: ${stats.totalNodes}`);
    console.log(`基础音频节点: ${stats.basicAudioNodes}`);
    console.log(`高级音频节点: ${stats.advancedAudioNodes}`);
    console.log(`音频系统节点: ${stats.audioSystemNodes}`);
    console.log(`音频优化节点: ${stats.audioOptimizationNodes}`);
    console.log(`基础输入节点: ${stats.basicInputNodes}`);
    console.log(`高级输入节点: ${stats.advancedInputNodes}`);
    console.log(`注册状态: ${stats.registered ? '已注册' : '未注册'}`);
  }
}

// 导出演示实例
export const audioInputNodesDemo = new AudioInputNodesDemo();

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  audioInputNodesDemo.runDemo().then(() => {
    audioInputNodesDemo.showRegistrationStats();
  });
}
