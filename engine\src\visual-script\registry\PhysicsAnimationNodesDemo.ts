/**
 * 批次12：物理与动画系统节点演示程序
 * 展示物理与动画系统节点的功能和使用方法
 */

import { PhysicsAnimationNodesRegistry } from './PhysicsAnimationNodesRegistry';
import { NodeRegistry } from './NodeRegistry';

/**
 * 物理与动画系统节点演示类
 */
export class PhysicsAnimationNodesDemo {
  private nodeRegistry: NodeRegistry;
  private physicsAnimationRegistry: PhysicsAnimationNodesRegistry;

  constructor() {
    this.nodeRegistry = new NodeRegistry();
    this.physicsAnimationRegistry = new PhysicsAnimationNodesRegistry(this.nodeRegistry);
  }

  /**
   * 运行完整演示
   */
  public async runDemo(): Promise<void> {
    console.log('🎬 开始物理与动画系统节点演示...\n');

    try {
      // 1. 注册所有节点
      await this.demonstrateRegistration();

      // 2. 演示物理系统节点
      await this.demonstratePhysicsNodes();

      // 3. 演示动画系统节点
      await this.demonstrateAnimationNodes();

      // 4. 演示节点组合使用
      await this.demonstrateNodeCombination();

      // 5. 性能测试
      await this.demonstratePerformance();

      console.log('✅ 物理与动画系统节点演示完成！');

    } catch (error) {
      console.error('❌ 演示过程中发生错误:', error);
    }
  }

  /**
   * 演示节点注册过程
   */
  private async demonstrateRegistration(): Promise<void> {
    console.log('📋 1. 节点注册演示');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // 注册所有节点
    this.physicsAnimationRegistry.registerAll();

    // 显示注册统计
    const info = this.physicsAnimationRegistry.getRegistryInfo();
    console.log(`✅ 成功注册 ${info.totalNodes} 个节点`);
    console.log(`   - 物理系统节点: ${info.physicsNodes} 个`);
    console.log(`   - 动画系统节点: ${info.animationNodes} 个`);

    // 验证注册
    const isValid = this.physicsAnimationRegistry.validateRegistration();
    console.log(`🔍 注册验证: ${isValid ? '通过' : '失败'}\n`);
  }

  /**
   * 演示物理系统节点
   */
  private async demonstratePhysicsNodes(): Promise<void> {
    console.log('🔧 2. 物理系统节点演示');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // 演示软体物理节点
    console.log('🌊 软体物理节点:');
    this.demonstrateNodeUsage('SoftBodyPhysics', {
      mesh: 'sphere',
      stiffness: 0.8,
      damping: 0.1
    });

    this.demonstrateNodeUsage('FluidSimulation', {
      particleCount: 1000,
      viscosity: 0.5,
      density: 1.0
    });

    this.demonstrateNodeUsage('ClothSimulation', {
      width: 10,
      height: 10,
      stiffness: 0.9
    });

    // 演示高级物理节点
    console.log('\n⚡ 高级物理节点:');
    this.demonstrateNodeUsage('RopeSimulation', {
      segments: 20,
      length: 5.0,
      thickness: 0.1
    });

    this.demonstrateNodeUsage('Destruction', {
      threshold: 100,
      fragmentCount: 50
    });

    // 演示物理优化节点
    console.log('\n🚀 物理优化节点:');
    this.demonstrateNodeUsage('PhysicsOptimization', {
      enableLOD: true,
      maxDistance: 100,
      updateFrequency: 60
    });

    console.log('');
  }

  /**
   * 演示动画系统节点
   */
  private async demonstrateAnimationNodes(): Promise<void> {
    console.log('🎬 3. 动画系统节点演示');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // 演示基础动画节点
    console.log('🎯 基础动画节点:');
    this.demonstrateNodeUsage('Tween', {
      target: 'position.x',
      from: 0,
      to: 10,
      duration: 2.0,
      easing: 'easeInOut'
    });

    this.demonstrateNodeUsage('KeyframeAnimation', {
      clip: 'walk_cycle',
      speed: 1.0,
      loop: true
    });

    this.demonstrateNodeUsage('AnimationStateMachine', {
      states: ['idle', 'walk', 'run'],
      currentState: 'idle'
    });

    // 演示高级动画节点
    console.log('\n🎨 高级动画节点:');
    this.demonstrateNodeUsage('IKSystem', {
      target: 'hand_target',
      chain: ['shoulder', 'elbow', 'wrist'],
      iterations: 10
    });

    this.demonstrateNodeUsage('AnimationBlend', {
      animations: ['walk', 'run'],
      weights: [0.7, 0.3]
    });

    // 演示动画工具节点
    console.log('\n🛠️ 动画工具节点:');
    this.demonstrateNodeUsage('AnimationBaking', {
      sourceAnimation: 'procedural_walk',
      frameRate: 30,
      duration: 5.0
    });

    console.log('');
  }

  /**
   * 演示节点组合使用
   */
  private async demonstrateNodeCombination(): Promise<void> {
    console.log('🔗 4. 节点组合使用演示');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    console.log('🎭 场景1: 布料角色动画');
    console.log('  1. 创建角色骨骼 → KeyframeAnimation');
    console.log('  2. 添加布料模拟 → ClothSimulation');
    console.log('  3. 设置IK约束 → IKSystem');
    console.log('  4. 优化性能 → PhysicsOptimization');

    console.log('\n🌊 场景2: 流体交互动画');
    console.log('  1. 创建流体系统 → FluidSimulation');
    console.log('  2. 添加交互动画 → AnimationBlend');
    console.log('  3. 设置动画事件 → AnimationEvent');
    console.log('  4. 性能监控 → PhysicsPerformanceMonitor');

    console.log('\n💥 场景3: 破坏效果动画');
    console.log('  1. 设置破坏触发 → Destruction');
    console.log('  2. 碎片动画 → Tween');
    console.log('  3. 状态切换 → AnimationStateMachine');
    console.log('  4. 动画烘焙 → AnimationBaking');

    console.log('');
  }

  /**
   * 演示性能测试
   */
  private async demonstratePerformance(): Promise<void> {
    console.log('⚡ 5. 性能测试演示');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // 测试节点创建性能
    const startTime = Date.now();
    
    for (let i = 0; i < 100; i++) {
      const softBodyNode = this.nodeRegistry.createNode('SoftBodyPhysics');
      const tweenNode = this.nodeRegistry.createNode('Tween');
      
      if (softBodyNode && tweenNode) {
        // 模拟节点使用
        softBodyNode.execute({ create: true });
        tweenNode.execute({ play: true });
      }
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`🚀 创建100个节点用时: ${duration}ms`);
    console.log(`📊 平均每个节点: ${(duration / 100).toFixed(2)}ms`);

    // 内存使用情况
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const memUsage = process.memoryUsage();
      console.log(`💾 内存使用: ${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`);
    }

    console.log('');
  }

  /**
   * 演示单个节点的使用
   */
  private demonstrateNodeUsage(nodeType: string, inputs: any): void {
    try {
      const node = this.nodeRegistry.createNode(nodeType);
      if (node) {
        console.log(`  ✓ ${nodeType}: 创建成功`);
        
        // 执行节点
        const result = node.execute(inputs);
        console.log(`    输入: ${JSON.stringify(inputs)}`);
        console.log(`    输出: ${result ? '成功' : '失败'}`);
      } else {
        console.log(`  ❌ ${nodeType}: 创建失败`);
      }
    } catch (error) {
      console.log(`  ⚠️ ${nodeType}: ${error}`);
    }
  }

  /**
   * 获取演示统计信息
   */
  public getDemoStats(): any {
    return {
      totalNodes: this.physicsAnimationRegistry.getRegisteredNodeCount(),
      physicsNodes: this.physicsAnimationRegistry.getPhysicsNodeCount(),
      animationNodes: this.physicsAnimationRegistry.getAnimationNodeCount(),
      registryInfo: this.physicsAnimationRegistry.getRegistryInfo(),
      isRegistered: this.physicsAnimationRegistry.isRegistered()
    };
  }
}

/**
 * 运行演示程序
 */
export async function runPhysicsAnimationNodesDemo(): Promise<void> {
  const demo = new PhysicsAnimationNodesDemo();
  await demo.runDemo();
}

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  runPhysicsAnimationNodesDemo().catch(console.error);
}
