/**
 * 渲染与网络系统节点注册表
 * 注册批次14：渲染与网络系统节点（12个节点）到编辑器
 * 包括：渲染系统节点(8个) + 网络系统节点(4个)
 */

import { NodeRegistry } from './NodeRegistry';

// 导入渲染系统节点
import {
  TessellationControlShaderNode,
  TessellationEvaluationShaderNode,
  GeometryShaderNode,
  ShaderLinkerNode
} from '../nodes/rendering/AdditionalShaderNodes';

import {
  ShaderVariantNode,
  ShaderParameterNode,
  ShaderIncludeNode,
  ShaderMacroNode
} from '../nodes/rendering/AdvancedShaderNodes';

// 导入网络系统节点
import {
  WebSocketNode,
  WebRTCNode,
  HTTPRequestNode,
  NetworkSyncNode
} from '../nodes/network/NetworkNodes';

/**
 * 渲染与网络系统节点注册表类
 */
export class RenderingNetworkNodesRegistry {
  private static instance: RenderingNetworkNodesRegistry;
  private registered: boolean = false;
  private nodeRegistry: typeof NodeRegistry;

  constructor() {
    this.nodeRegistry = NodeRegistry;
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): RenderingNetworkNodesRegistry {
    if (!RenderingNetworkNodesRegistry.instance) {
      RenderingNetworkNodesRegistry.instance = new RenderingNetworkNodesRegistry();
    }
    return RenderingNetworkNodesRegistry.instance;
  }

  /**
   * 注册所有渲染与网络系统节点
   */
  public registerAllNodes(): void {
    if (this.registered) {
      console.log('渲染与网络系统节点已经注册过了');
      return;
    }

    try {
      console.log('开始注册批次14：渲染与网络系统节点...');

      // 注册高级着色器节点（4个）
      this.registerAdvancedShaderNodes();

      // 注册着色器管理节点（4个）
      this.registerShaderManagementNodes();

      // 注册网络系统节点（4个）
      this.registerNetworkSystemNodes();

      this.registered = true;
      console.log('✅ 批次14渲染与网络系统节点注册完成: 12个节点');
      console.log('  - 高级着色器节点: 4个');
      console.log('  - 着色器管理节点: 4个');
      console.log('  - 网络系统节点: 4个');

    } catch (error) {
      console.error('❌ 渲染与网络系统节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册高级着色器节点（4个）
   */
  private registerAdvancedShaderNodes(): void {
    console.log('注册高级着色器节点...');

    // 曲面细分控制着色器节点
    this.nodeRegistry.registerNode('rendering/tessellationControl', TessellationControlShaderNode, {
      category: '渲染系统',
      description: '编译和管理曲面细分控制着色器程序',
      inputs: ['compile', 'source', 'shaderId', 'patchVertices'],
      outputs: ['shader', 'shaderId', 'compiled', 'compilationTime', 'onCompiled', 'onError']
    });

    // 曲面细分评估着色器节点
    this.nodeRegistry.registerNode('rendering/tessellationEvaluation', TessellationEvaluationShaderNode, {
      category: '渲染系统',
      description: '编译和管理曲面细分评估着色器程序',
      inputs: ['compile', 'source', 'shaderId', 'primitiveMode'],
      outputs: ['shader', 'shaderId', 'compiled', 'compilationTime', 'onCompiled', 'onError']
    });

    // 几何着色器节点
    this.nodeRegistry.registerNode('rendering/geometryShader', GeometryShaderNode, {
      category: '渲染系统',
      description: '编译和管理几何着色器程序',
      inputs: ['compile', 'source', 'shaderId', 'inputPrimitive', 'outputPrimitive', 'maxVertices'],
      outputs: ['shader', 'shaderId', 'compiled', 'compilationTime', 'onCompiled', 'onError']
    });

    // 着色器链接器节点
    this.nodeRegistry.registerNode('rendering/shaderLinker', ShaderLinkerNode, {
      category: '渲染系统',
      description: '链接多个着色器阶段为完整程序',
      inputs: ['link', 'vertexShader', 'fragmentShader', 'geometryShader', 'tessControlShader', 'tessEvalShader', 'programId'],
      outputs: ['program', 'programId', 'linked', 'linkTime', 'onLinked', 'onError']
    });

    console.log('✅ 高级着色器节点注册完成: 4个');
  }

  /**
   * 注册着色器管理节点（4个）
   */
  private registerShaderManagementNodes(): void {
    console.log('注册着色器管理节点...');

    // 着色器变体节点
    this.nodeRegistry.registerNode('rendering/shaderVariant', ShaderVariantNode, {
      category: '渲染系统',
      description: '管理着色器变体和条件编译',
      inputs: ['create', 'variantName', 'baseShader', 'defines', 'keywords'],
      outputs: ['variant', 'variantId', 'compiled', 'activeKeywords', 'onCreate', 'onError']
    });

    // 着色器参数节点
    this.nodeRegistry.registerNode('rendering/shaderParameter', ShaderParameterNode, {
      category: '渲染系统',
      description: '管理着色器参数和Uniform变量',
      inputs: ['set', 'shaderId', 'parameterName', 'value', 'type'],
      outputs: ['parameter', 'currentValue', 'parameterType', 'isSet', 'onSet', 'onError']
    });

    // 着色器包含节点
    this.nodeRegistry.registerNode('rendering/shaderInclude', ShaderIncludeNode, {
      category: '渲染系统',
      description: '管理着色器包含文件和代码复用',
      inputs: ['register', 'includeName', 'includeContent', 'dependencies'],
      outputs: ['include', 'includePath', 'content', 'isRegistered', 'onRegister', 'onError']
    });

    // 着色器宏节点
    this.nodeRegistry.registerNode('rendering/shaderMacro', ShaderMacroNode, {
      category: '渲染系统',
      description: '管理着色器宏定义和条件编译',
      inputs: ['define', 'macroName', 'macroValue', 'condition', 'enabled'],
      outputs: ['macro', 'macroDefinition', 'isActive', 'expandedValue', 'onDefine', 'onError']
    });

    console.log('✅ 着色器管理节点注册完成: 4个');
  }

  /**
   * 注册网络系统节点（4个）
   */
  private registerNetworkSystemNodes(): void {
    console.log('注册网络系统节点...');

    // WebSocket连接节点
    this.nodeRegistry.registerNode('network/webSocket', WebSocketNode, {
      category: '网络系统',
      description: '创建和管理WebSocket连接',
      inputs: ['connect', 'disconnect', 'send', 'url', 'protocols', 'message', 'connectionId'],
      outputs: ['connection', 'connectionId', 'state', 'receivedMessage', 'onConnected', 'onDisconnected', 'onMessage', 'onError']
    });

    // WebRTC连接节点
    this.nodeRegistry.registerNode('network/webRTC', WebRTCNode, {
      category: '网络系统',
      description: '创建和管理WebRTC点对点连接',
      inputs: ['createOffer', 'createAnswer', 'setRemoteDescription', 'addIceCandidate', 'sendData', 'connectionId', 'sdp', 'candidate', 'data'],
      outputs: ['connection', 'connectionId', 'localDescription', 'remoteDescription', 'iceCandidate', 'dataChannel', 'receivedData', 'onConnected', 'onDataReceived', 'onError']
    });

    // HTTP请求节点
    this.nodeRegistry.registerNode('network/httpRequest', HTTPRequestNode, {
      category: '网络系统',
      description: '发送HTTP请求并处理响应',
      inputs: ['send', 'url', 'method', 'headers', 'body', 'timeout', 'retries', 'credentials'],
      outputs: ['response', 'data', 'status', 'statusText', 'headers', 'ok', 'onSuccess', 'onError']
    });

    // 网络同步节点
    this.nodeRegistry.registerNode('network/networkSync', NetworkSyncNode, {
      category: '网络系统',
      description: '实现多客户端状态同步',
      inputs: ['sync', 'subscribe', 'unsubscribe', 'objectId', 'state', 'syncMode', 'priority'],
      outputs: ['syncedState', 'objectId', 'lastSyncTime', 'syncStatus', 'conflictResolution', 'onSync', 'onConflict', 'onError']
    });

    console.log('✅ 网络系统节点注册完成: 4个');
  }

  /**
   * 获取所有已注册的节点类型名称
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 高级着色器节点（4个）
      'rendering/tessellationControl',
      'rendering/tessellationEvaluation',
      'rendering/geometryShader',
      'rendering/shaderLinker',

      // 着色器管理节点（4个）
      'rendering/shaderVariant',
      'rendering/shaderParameter',
      'rendering/shaderInclude',
      'rendering/shaderMacro',

      // 网络系统节点（4个）
      'network/webSocket',
      'network/webRTC',
      'network/httpRequest',
      'network/networkSync'
    ];
  }

  /**
   * 检查节点是否已注册
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 获取注册统计信息
   */
  public getRegistrationStats(): any {
    return {
      totalNodes: 12,
      advancedShaderNodes: 4,
      shaderManagementNodes: 4,
      networkSystemNodes: 4,
      registered: this.registered,
      registryName: 'RenderingNetworkNodesRegistry'
    };
  }
}

// 导出单例实例
export const renderingNetworkNodesRegistry = RenderingNetworkNodesRegistry.getInstance();

// 自动注册（可选）
if (typeof window !== 'undefined') {
  // 在浏览器环境中自动注册
  renderingNetworkNodesRegistry.registerAllNodes();
}
