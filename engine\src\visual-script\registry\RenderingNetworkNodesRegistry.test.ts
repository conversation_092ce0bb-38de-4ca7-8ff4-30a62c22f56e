/**
 * 渲染与网络系统节点注册表测试
 */

import { RenderingNetworkNodesRegistry } from './RenderingNetworkNodesRegistry';
import { NodeRegistry } from './NodeRegistry';

describe('RenderingNetworkNodesRegistry', () => {
  let registry: RenderingNetworkNodesRegistry;
  let nodeRegistry: typeof NodeRegistry;

  beforeEach(() => {
    registry = RenderingNetworkNodesRegistry.getInstance();
    nodeRegistry = NodeRegistry;
  });

  afterEach(() => {
    // 清理注册的节点
    const nodeTypes = registry.getAllRegisteredNodeTypes();
    nodeTypes.forEach(type => {
      nodeRegistry.unregisterNode(type);
    });
  });

  describe('单例模式', () => {
    it('应该返回同一个实例', () => {
      const instance1 = RenderingNetworkNodesRegistry.getInstance();
      const instance2 = RenderingNetworkNodesRegistry.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('节点注册', () => {
    it('应该成功注册所有渲染与网络系统节点', () => {
      expect(() => registry.registerAllNodes()).not.toThrow();
      expect(registry.isRegistered()).toBe(true);
    });

    it('应该注册正确数量的节点', () => {
      registry.registerAllNodes();
      const stats = registry.getRegistrationStats();
      
      expect(stats.totalNodes).toBe(12);
      expect(stats.advancedShaderNodes).toBe(4);
      expect(stats.shaderManagementNodes).toBe(4);
      expect(stats.networkSystemNodes).toBe(4);
    });

    it('应该防止重复注册', () => {
      registry.registerAllNodes();
      const consoleSpy = jest.spyOn(console, 'log');
      
      registry.registerAllNodes(); // 第二次注册
      
      expect(consoleSpy).toHaveBeenCalledWith('渲染与网络系统节点已经注册过了');
    });
  });

  describe('高级着色器节点注册', () => {
    it('应该注册曲面细分控制着色器节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('rendering/tessellationControl')).toBe(true);
    });

    it('应该注册曲面细分评估着色器节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('rendering/tessellationEvaluation')).toBe(true);
    });

    it('应该注册几何着色器节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('rendering/geometryShader')).toBe(true);
    });

    it('应该注册着色器链接器节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('rendering/shaderLinker')).toBe(true);
    });
  });

  describe('着色器管理节点注册', () => {
    it('应该注册着色器变体节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('rendering/shaderVariant')).toBe(true);
    });

    it('应该注册着色器参数节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('rendering/shaderParameter')).toBe(true);
    });

    it('应该注册着色器包含节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('rendering/shaderInclude')).toBe(true);
    });

    it('应该注册着色器宏节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('rendering/shaderMacro')).toBe(true);
    });
  });

  describe('网络系统节点注册', () => {
    it('应该注册WebSocket连接节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('network/webSocket')).toBe(true);
    });

    it('应该注册WebRTC连接节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('network/webRTC')).toBe(true);
    });

    it('应该注册HTTP请求节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('network/httpRequest')).toBe(true);
    });

    it('应该注册网络同步节点', () => {
      registry.registerAllNodes();
      expect(nodeRegistry.hasNode('network/networkSync')).toBe(true);
    });
  });

  describe('节点类型列表', () => {
    it('应该返回所有注册的节点类型', () => {
      const nodeTypes = registry.getAllRegisteredNodeTypes();
      expect(nodeTypes).toHaveLength(12);
      
      // 检查高级着色器节点
      expect(nodeTypes).toContain('rendering/tessellationControl');
      expect(nodeTypes).toContain('rendering/tessellationEvaluation');
      expect(nodeTypes).toContain('rendering/geometryShader');
      expect(nodeTypes).toContain('rendering/shaderLinker');
      
      // 检查着色器管理节点
      expect(nodeTypes).toContain('rendering/shaderVariant');
      expect(nodeTypes).toContain('rendering/shaderParameter');
      expect(nodeTypes).toContain('rendering/shaderInclude');
      expect(nodeTypes).toContain('rendering/shaderMacro');
      
      // 检查网络系统节点
      expect(nodeTypes).toContain('network/webSocket');
      expect(nodeTypes).toContain('network/webRTC');
      expect(nodeTypes).toContain('network/httpRequest');
      expect(nodeTypes).toContain('network/networkSync');
    });
  });

  describe('统计信息', () => {
    it('应该返回正确的注册统计信息', () => {
      registry.registerAllNodes();
      const stats = registry.getRegistrationStats();
      
      expect(stats).toEqual({
        totalNodes: 12,
        advancedShaderNodes: 4,
        shaderManagementNodes: 4,
        networkSystemNodes: 4,
        registered: true,
        registryName: 'RenderingNetworkNodesRegistry'
      });
    });
  });

  describe('错误处理', () => {
    it('应该处理注册过程中的错误', () => {
      // 模拟注册错误
      const originalRegisterNode = nodeRegistry.registerNode;
      nodeRegistry.registerNode = jest.fn().mockImplementation(() => {
        throw new Error('注册失败');
      });

      expect(() => registry.registerAllNodes()).toThrow('注册失败');
      
      // 恢复原始方法
      nodeRegistry.registerNode = originalRegisterNode;
    });
  });

  describe('浏览器环境自动注册', () => {
    it('应该在浏览器环境中自动注册', () => {
      // 模拟浏览器环境
      const originalWindow = global.window;
      global.window = {} as any;
      
      const registerSpy = jest.spyOn(registry, 'registerAllNodes');
      
      // 重新导入模块以触发自动注册
      jest.resetModules();
      require('./RenderingNetworkNodesRegistry');
      
      // 恢复环境
      global.window = originalWindow;
    });
  });
});

/**
 * 集成测试
 */
describe('RenderingNetworkNodesRegistry 集成测试', () => {
  let registry: RenderingNetworkNodesRegistry;

  beforeEach(() => {
    registry = RenderingNetworkNodesRegistry.getInstance();
  });

  it('应该与NodeRegistry正确集成', () => {
    registry.registerAllNodes();
    
    const nodeRegistry = NodeRegistry;
    const registeredTypes = registry.getAllRegisteredNodeTypes();
    
    // 验证所有节点都已在NodeRegistry中注册
    registeredTypes.forEach(type => {
      expect(nodeRegistry.hasNode(type)).toBe(true);
    });
  });

  it('应该支持节点的创建和执行', () => {
    registry.registerAllNodes();
    
    const nodeRegistry = NodeRegistry;
    
    // 测试创建渲染节点
    const shaderNode = nodeRegistry.createNode('rendering/tessellationControl');
    expect(shaderNode).toBeDefined();
    
    // 测试创建网络节点
    const networkNode = nodeRegistry.createNode('network/webSocket');
    expect(networkNode).toBeDefined();
  });

  it('应该支持渲染管线集成', () => {
    registry.registerAllNodes();
    
    const nodeRegistry = NodeRegistry;
    
    // 测试着色器管线
    const tessControlNode = nodeRegistry.createNode('rendering/tessellationControl');
    const tessEvalNode = nodeRegistry.createNode('rendering/tessellationEvaluation');
    const geometryNode = nodeRegistry.createNode('rendering/geometryShader');
    const linkerNode = nodeRegistry.createNode('rendering/shaderLinker');
    
    expect(tessControlNode).toBeDefined();
    expect(tessEvalNode).toBeDefined();
    expect(geometryNode).toBeDefined();
    expect(linkerNode).toBeDefined();
  });

  it('应该支持网络通信集成', () => {
    registry.registerAllNodes();
    
    const nodeRegistry = NodeRegistry;
    
    // 测试网络通信管线
    const webSocketNode = nodeRegistry.createNode('network/webSocket');
    const webRTCNode = nodeRegistry.createNode('network/webRTC');
    const httpNode = nodeRegistry.createNode('network/httpRequest');
    const syncNode = nodeRegistry.createNode('network/networkSync');
    
    expect(webSocketNode).toBeDefined();
    expect(webRTCNode).toBeDefined();
    expect(httpNode).toBeDefined();
    expect(syncNode).toBeDefined();
  });
});
