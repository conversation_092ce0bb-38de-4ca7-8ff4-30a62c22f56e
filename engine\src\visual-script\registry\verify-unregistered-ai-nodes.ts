/**
 * 验证批次11：AI与计算机视觉节点注册表
 * 确保所有40个节点正确注册和工作
 */

import { UnregisteredAINodesRegistry, UNREGISTERED_AI_NODE_TYPES } from './UnregisteredAINodesRegistry';
import { NodeRegistry } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

/**
 * 验证AI与计算机视觉节点注册表
 */
async function verifyUnregisteredAINodes(): Promise<void> {
  Debug.log('VerifyUnregisteredAINodes', '=== 开始验证AI与计算机视觉节点注册表 ===');

  try {
    // 1. 获取注册表实例
    const registry = UnregisteredAINodesRegistry.getInstance();
    
    // 2. 注册所有节点
    Debug.log('VerifyUnregisteredAINodes', '注册所有AI与计算机视觉节点...');
    registry.registerAllNodes();
    
    // 3. 验证注册状态
    const isRegistered = registry.isRegistered();
    Debug.log('VerifyUnregisteredAINodes', `注册状态: ${isRegistered ? '成功' : '失败'}`);
    
    if (!isRegistered) {
      throw new Error('节点注册失败');
    }
    
    // 4. 验证节点数量
    const allNodeTypes = registry.getAllRegisteredNodeTypes();
    Debug.log('VerifyUnregisteredAINodes', `注册节点总数: ${allNodeTypes.length}`);
    
    if (allNodeTypes.length !== 40) {
      throw new Error(`期望40个节点，实际注册了${allNodeTypes.length}个节点`);
    }
    
    // 5. 验证节点分类
    const categories = registry.getNodeCategories();
    Debug.log('VerifyUnregisteredAINodes', `节点分类数: ${categories.size}`);
    
    const expectedCategories = ['模型管理', 'AutoML', '模型优化', '计算机视觉', '深度学习'];
    for (const category of expectedCategories) {
      if (!categories.has(category)) {
        throw new Error(`缺少分类: ${category}`);
      }
      const nodes = registry.getNodesByCategory(category);
      Debug.log('VerifyUnregisteredAINodes', `${category}: ${nodes.length}个节点`);
    }
    
    // 6. 验证关键节点类型
    const keyNodeTypes = [
      UNREGISTERED_AI_NODE_TYPES.MODEL_DEPLOYMENT,
      UNREGISTERED_AI_NODE_TYPES.AUTO_ML,
      UNREGISTERED_AI_NODE_TYPES.IMAGE_SEGMENTATION,
      UNREGISTERED_AI_NODE_TYPES.TRANSFORMER_MODEL,
      UNREGISTERED_AI_NODE_TYPES.GAN_MODEL
    ];
    
    for (const nodeType of keyNodeTypes) {
      if (!registry.hasNodeType(nodeType)) {
        throw new Error(`关键节点类型未注册: ${nodeType}`);
      }
      Debug.log('VerifyUnregisteredAINodes', `✓ 节点类型已注册: ${nodeType}`);
    }
    
    // 7. 验证节点实例化
    Debug.log('VerifyUnregisteredAINodes', '验证节点实例化...');
    for (const nodeType of keyNodeTypes) {
      try {
        const nodeInstance = registry.createNode(nodeType);
        if (!nodeInstance) {
          throw new Error(`节点实例化失败: ${nodeType}`);
        }
        Debug.log('VerifyUnregisteredAINodes', `✓ 节点实例化成功: ${nodeType}`);
      } catch (error) {
        throw new Error(`节点实例化失败 ${nodeType}: ${error.message}`);
      }
    }
    
    // 8. 验证与全局注册表的集成
    Debug.log('VerifyUnregisteredAINodes', '验证与全局注册表的集成...');
    for (const nodeType of keyNodeTypes) {
      if (!NodeRegistry.hasNodeType(nodeType)) {
        throw new Error(`节点未在全局注册表中注册: ${nodeType}`);
      }
      
      try {
        const nodeInstance = NodeRegistry.createNode(nodeType);
        if (!nodeInstance) {
          throw new Error(`通过全局注册表实例化失败: ${nodeType}`);
        }
        Debug.log('VerifyUnregisteredAINodes', `✓ 全局注册表实例化成功: ${nodeType}`);
      } catch (error) {
        throw new Error(`全局注册表实例化失败 ${nodeType}: ${error.message}`);
      }
    }
    
    // 9. 验证节点功能
    Debug.log('VerifyUnregisteredAINodes', '验证节点基本功能...');
    await verifyNodeFunctionality();
    
    // 10. 验证注册表验证功能
    const validationResult = registry.validateRegistration();
    if (!validationResult) {
      throw new Error('注册表验证失败');
    }
    Debug.log('VerifyUnregisteredAINodes', '✓ 注册表验证通过');
    
    Debug.log('VerifyUnregisteredAINodes', '=== AI与计算机视觉节点注册表验证完成 ===');
    Debug.log('VerifyUnregisteredAINodes', '所有验证项目均通过！');
    
  } catch (error) {
    Debug.error('VerifyUnregisteredAINodes', '验证失败:', error);
    throw error;
  }
}

/**
 * 验证节点基本功能
 */
async function verifyNodeFunctionality(): Promise<void> {
  const registry = UnregisteredAINodesRegistry.getInstance();
  
  // 测试模型部署节点
  try {
    const deploymentNode = registry.createNode(UNREGISTERED_AI_NODE_TYPES.MODEL_DEPLOYMENT);
    const result = deploymentNode.execute({
      modelId: 'test-model',
      environment: 'development',
      autoScale: false
    });
    
    if (!result || typeof result !== 'object') {
      throw new Error('模型部署节点执行结果无效');
    }
    Debug.log('VerifyUnregisteredAINodes', '✓ 模型部署节点功能验证通过');
  } catch (error) {
    throw new Error(`模型部署节点功能验证失败: ${error.message}`);
  }
  
  // 测试图像分割节点
  try {
    const segmentationNode = registry.createNode(UNREGISTERED_AI_NODE_TYPES.IMAGE_SEGMENTATION);
    const result = segmentationNode.execute({
      image: 'test_image_data',
      modelId: 'test_segmentation_model',
      threshold: 0.5
    });
    
    if (!result || typeof result !== 'object') {
      throw new Error('图像分割节点执行结果无效');
    }
    Debug.log('VerifyUnregisteredAINodes', '✓ 图像分割节点功能验证通过');
  } catch (error) {
    throw new Error(`图像分割节点功能验证失败: ${error.message}`);
  }
  
  // 测试Transformer节点
  try {
    const transformerNode = registry.createNode(UNREGISTERED_AI_NODE_TYPES.TRANSFORMER_MODEL);
    const result = transformerNode.execute({
      inputSequence: [1, 2, 3, 4, 5],
      modelConfig: {
        numLayers: 2,
        numHeads: 4,
        hiddenSize: 128
      }
    });
    
    if (!result || typeof result !== 'object') {
      throw new Error('Transformer节点执行结果无效');
    }
    Debug.log('VerifyUnregisteredAINodes', '✓ Transformer节点功能验证通过');
  } catch (error) {
    throw new Error(`Transformer节点功能验证失败: ${error.message}`);
  }
}

/**
 * 生成验证报告
 */
function generateVerificationReport(): void {
  const registry = UnregisteredAINodesRegistry.getInstance();
  
  Debug.log('VerifyUnregisteredAINodes', '=== 验证报告 ===');
  Debug.log('VerifyUnregisteredAINodes', `注册状态: ${registry.isRegistered() ? '已注册' : '未注册'}`);
  Debug.log('VerifyUnregisteredAINodes', `节点总数: ${registry.getAllRegisteredNodeTypes().length}`);
  
  const categories = registry.getNodeCategories();
  Debug.log('VerifyUnregisteredAINodes', `分类总数: ${categories.size}`);
  
  for (const [category, nodes] of categories) {
    Debug.log('VerifyUnregisteredAINodes', `  ${category}: ${nodes.length}个节点`);
  }
  
  Debug.log('VerifyUnregisteredAINodes', '=== 报告结束 ===');
}

// 如果直接运行此文件，执行验证
if (require.main === module) {
  verifyUnregisteredAINodes()
    .then(() => {
      generateVerificationReport();
      console.log('✅ AI与计算机视觉节点注册表验证成功！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ AI与计算机视觉节点注册表验证失败:', error.message);
      process.exit(1);
    });
}

export { verifyUnregisteredAINodes, generateVerificationReport };
