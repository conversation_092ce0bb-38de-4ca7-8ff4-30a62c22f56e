/**
 * 渲染与网络系统节点演示程序
 * 展示批次14渲染与网络系统节点的功能和用法
 */

import { RenderingNetworkNodesRegistry } from './RenderingNetworkNodesRegistry';
import { NodeRegistry } from './NodeRegistry';

/**
 * 渲染与网络系统节点演示类
 */
export class RenderingNetworkNodesDemo {
  private registry: RenderingNetworkNodesRegistry;
  private nodeRegistry: typeof NodeRegistry;

  constructor() {
    this.registry = RenderingNetworkNodesRegistry.getInstance();
    this.nodeRegistry = NodeRegistry;
  }

  /**
   * 运行完整演示
   */
  public async runDemo(): Promise<void> {
    console.log('🎨 渲染与网络系统节点演示开始');
    console.log('=====================================');

    try {
      // 注册所有节点
      this.registry.registerAllNodes();
      console.log('✅ 节点注册完成');

      // 演示高级着色器功能
      await this.demoAdvancedShaders();

      // 演示着色器管理功能
      await this.demoShaderManagement();

      // 演示网络系统功能
      await this.demoNetworkSystem();

      // 演示综合应用场景
      await this.demoIntegratedScenarios();

      console.log('=====================================');
      console.log('🎉 渲染与网络系统节点演示完成');

    } catch (error) {
      console.error('❌ 演示过程中发生错误:', error);
    }
  }

  /**
   * 演示高级着色器功能
   */
  private async demoAdvancedShaders(): Promise<void> {
    console.log('\n🔺 高级着色器功能演示');
    console.log('-------------------');

    // 曲面细分控制着色器演示
    const tessControlNode = this.nodeRegistry.createNode('rendering/tessellationControl');
    const tessControlResult = tessControlNode.execute({
      compile: true,
      source: `
        #version 450 core
        layout(vertices = 3) out;
        void main() {
          gl_TessLevelOuter[0] = 4.0;
          gl_TessLevelOuter[1] = 4.0;
          gl_TessLevelOuter[2] = 4.0;
          gl_TessLevelInner[0] = 4.0;
        }
      `,
      shaderId: 'tess_control_shader',
      patchVertices: 3
    });
    console.log('曲面细分控制着色器结果:', tessControlResult);

    // 曲面细分评估着色器演示
    const tessEvalNode = this.nodeRegistry.createNode('rendering/tessellationEvaluation');
    const tessEvalResult = tessEvalNode.execute({
      compile: true,
      source: `
        #version 450 core
        layout(triangles, equal_spacing, cw) in;
        void main() {
          vec3 p0 = gl_in[0].gl_Position.xyz;
          vec3 p1 = gl_in[1].gl_Position.xyz;
          vec3 p2 = gl_in[2].gl_Position.xyz;
          vec3 pos = gl_TessCoord.x * p0 + gl_TessCoord.y * p1 + gl_TessCoord.z * p2;
          gl_Position = vec4(pos, 1.0);
        }
      `,
      shaderId: 'tess_eval_shader',
      primitiveMode: 'triangles'
    });
    console.log('曲面细分评估着色器结果:', tessEvalResult);

    // 几何着色器演示
    const geometryNode = this.nodeRegistry.createNode('rendering/geometryShader');
    const geometryResult = geometryNode.execute({
      compile: true,
      source: `
        #version 450 core
        layout(triangles) in;
        layout(triangle_strip, max_vertices = 3) out;
        void main() {
          for(int i = 0; i < 3; i++) {
            gl_Position = gl_in[i].gl_Position;
            EmitVertex();
          }
          EndPrimitive();
        }
      `,
      shaderId: 'geometry_shader',
      inputPrimitive: 'triangles',
      outputPrimitive: 'triangle_strip',
      maxVertices: 3
    });
    console.log('几何着色器结果:', geometryResult);

    // 着色器链接器演示
    const linkerNode = this.nodeRegistry.createNode('rendering/shaderLinker');
    const linkerResult = linkerNode.execute({
      link: true,
      vertexShader: 'vertex_shader_id',
      fragmentShader: 'fragment_shader_id',
      geometryShader: geometryResult.shaderId,
      tessControlShader: tessControlResult.shaderId,
      tessEvalShader: tessEvalResult.shaderId,
      programId: 'advanced_shader_program'
    });
    console.log('着色器链接器结果:', linkerResult);
  }

  /**
   * 演示着色器管理功能
   */
  private async demoShaderManagement(): Promise<void> {
    console.log('\n⚙️ 着色器管理功能演示');
    console.log('-------------------');

    // 着色器变体演示
    const variantNode = this.nodeRegistry.createNode('rendering/shaderVariant');
    const variantResult = variantNode.execute({
      create: true,
      variantName: 'lit_variant',
      baseShader: 'base_shader',
      defines: ['USE_LIGHTING', 'USE_SHADOWS'],
      keywords: ['DIRECTIONAL_LIGHT', 'POINT_LIGHT']
    });
    console.log('着色器变体结果:', variantResult);

    // 着色器参数演示
    const parameterNode = this.nodeRegistry.createNode('rendering/shaderParameter');
    const parameterResult = parameterNode.execute({
      set: true,
      shaderId: 'advanced_shader_program',
      parameterName: 'u_lightColor',
      value: [1.0, 1.0, 1.0, 1.0],
      type: 'vec4'
    });
    console.log('着色器参数结果:', parameterResult);

    // 着色器包含演示
    const includeNode = this.nodeRegistry.createNode('rendering/shaderInclude');
    const includeResult = includeNode.execute({
      register: true,
      includeName: 'lighting.glsl',
      includeContent: `
        vec3 calculateLighting(vec3 normal, vec3 lightDir, vec3 lightColor) {
          float NdotL = max(dot(normal, lightDir), 0.0);
          return lightColor * NdotL;
        }
      `,
      dependencies: []
    });
    console.log('着色器包含结果:', includeResult);

    // 着色器宏演示
    const macroNode = this.nodeRegistry.createNode('rendering/shaderMacro');
    const macroResult = macroNode.execute({
      define: true,
      macroName: 'MAX_LIGHTS',
      macroValue: '8',
      condition: 'USE_LIGHTING',
      enabled: true
    });
    console.log('着色器宏结果:', macroResult);
  }

  /**
   * 演示网络系统功能
   */
  private async demoNetworkSystem(): Promise<void> {
    console.log('\n🌐 网络系统功能演示');
    console.log('-------------------');

    // WebSocket连接演示
    const webSocketNode = this.nodeRegistry.createNode('network/webSocket');
    const webSocketResult = webSocketNode.execute({
      connect: true,
      url: 'ws://localhost:8080/websocket',
      protocols: ['chat', 'superchat'],
      connectionId: 'main_websocket'
    });
    console.log('WebSocket连接结果:', webSocketResult);

    // WebRTC连接演示
    const webRTCNode = this.nodeRegistry.createNode('network/webRTC');
    const webRTCResult = webRTCNode.execute({
      createOffer: true,
      connectionId: 'peer_connection_1'
    });
    console.log('WebRTC连接结果:', webRTCResult);

    // HTTP请求演示
    const httpNode = this.nodeRegistry.createNode('network/httpRequest');
    const httpResult = httpNode.execute({
      send: true,
      url: 'https://api.example.com/data',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer token123'
      },
      timeout: 5000,
      retries: 3
    });
    console.log('HTTP请求结果:', httpResult);

    // 网络同步演示
    const syncNode = this.nodeRegistry.createNode('network/networkSync');
    const syncResult = syncNode.execute({
      sync: true,
      objectId: 'player_position',
      state: { x: 100, y: 50, z: 200 },
      syncMode: 'realtime',
      priority: 'high'
    });
    console.log('网络同步结果:', syncResult);
  }

  /**
   * 演示综合应用场景
   */
  private async demoIntegratedScenarios(): Promise<void> {
    console.log('\n🎮 综合应用场景演示');
    console.log('-------------------');

    // 场景1: 多人在线渲染
    console.log('场景1: 多人在线渲染');
    await this.demoMultiplayerRendering();

    // 场景2: 实时着色器编辑
    console.log('\n场景2: 实时着色器编辑');
    await this.demoRealtimeShaderEditing();

    // 场景3: 网络化渲染管线
    console.log('\n场景3: 网络化渲染管线');
    await this.demoNetworkedRenderingPipeline();
  }

  /**
   * 多人在线渲染场景
   */
  private async demoMultiplayerRendering(): Promise<void> {
    // 建立WebSocket连接用于实时通信
    const webSocketNode = this.nodeRegistry.createNode('network/webSocket');
    const connection = webSocketNode.execute({
      connect: true,
      url: 'ws://game-server.com/multiplayer',
      connectionId: 'multiplayer_session'
    });

    // 同步玩家状态
    const syncNode = this.nodeRegistry.createNode('network/networkSync');
    const playerSync = syncNode.execute({
      sync: true,
      objectId: 'player_1',
      state: { position: [10, 0, 5], rotation: [0, 45, 0] },
      syncMode: 'interpolated'
    });

    // 创建专用着色器变体用于多人渲染
    const variantNode = this.nodeRegistry.createNode('rendering/shaderVariant');
    const multiplayerShader = variantNode.execute({
      create: true,
      variantName: 'multiplayer_player',
      baseShader: 'character_shader',
      defines: ['MULTIPLAYER_MODE', 'NETWORK_INTERPOLATION']
    });

    console.log('多人在线渲染设置完成:', {
      connection: connection.connectionId,
      playerSync: playerSync.objectId,
      shader: multiplayerShader.variantId
    });
  }

  /**
   * 实时着色器编辑场景
   */
  private async demoRealtimeShaderEditing(): Promise<void> {
    // 建立WebRTC连接用于协作编辑
    const webRTCNode = this.nodeRegistry.createNode('network/webRTC');
    const rtcConnection = webRTCNode.execute({
      createOffer: true,
      connectionId: 'shader_collaboration'
    });

    // 注册着色器包含文件
    const includeNode = this.nodeRegistry.createNode('rendering/shaderInclude');
    const shaderInclude = includeNode.execute({
      register: true,
      includeName: 'collaborative_utils.glsl',
      includeContent: '// 协作编辑工具函数'
    });

    // 编译实时着色器
    const tessControlNode = this.nodeRegistry.createNode('rendering/tessellationControl');
    const realtimeShader = tessControlNode.execute({
      compile: true,
      source: '#include "collaborative_utils.glsl"\n// 实时编辑的着色器代码',
      shaderId: 'realtime_collaborative_shader'
    });

    // 同步着色器状态
    const syncNode = this.nodeRegistry.createNode('network/networkSync');
    const shaderSync = syncNode.execute({
      sync: true,
      objectId: 'shader_state',
      state: { version: 1, lastEdit: Date.now() },
      syncMode: 'immediate'
    });

    console.log('实时着色器编辑设置完成:', {
      collaboration: rtcConnection.connectionId,
      shader: realtimeShader.shaderId,
      sync: shaderSync.objectId
    });
  }

  /**
   * 网络化渲染管线场景
   */
  private async demoNetworkedRenderingPipeline(): Promise<void> {
    // 通过HTTP获取渲染配置
    const httpNode = this.nodeRegistry.createNode('network/httpRequest');
    const renderConfig = httpNode.execute({
      send: true,
      url: 'https://render-service.com/api/pipeline-config',
      method: 'GET'
    });

    // 设置着色器宏基于网络配置
    const macroNode = this.nodeRegistry.createNode('rendering/shaderMacro');
    const networkMacro = macroNode.execute({
      define: true,
      macroName: 'NETWORK_QUALITY',
      macroValue: 'HIGH',
      condition: 'NETWORK_RENDERING'
    });

    // 创建网络优化的着色器变体
    const variantNode = this.nodeRegistry.createNode('rendering/shaderVariant');
    const networkShader = variantNode.execute({
      create: true,
      variantName: 'network_optimized',
      baseShader: 'standard_shader',
      defines: ['NETWORK_RENDERING', 'BANDWIDTH_OPTIMIZATION']
    });

    // 链接完整的网络化渲染程序
    const linkerNode = this.nodeRegistry.createNode('rendering/shaderLinker');
    const networkProgram = linkerNode.execute({
      link: true,
      vertexShader: 'network_vertex',
      fragmentShader: 'network_fragment',
      programId: 'networked_rendering_program'
    });

    console.log('网络化渲染管线设置完成:', {
      config: renderConfig.status,
      macro: networkMacro.macroDefinition,
      shader: networkShader.variantId,
      program: networkProgram.programId
    });
  }

  /**
   * 显示注册统计信息
   */
  public showRegistrationStats(): void {
    const stats = this.registry.getRegistrationStats();
    console.log('\n📊 注册统计信息');
    console.log('================');
    console.log(`总节点数: ${stats.totalNodes}`);
    console.log(`高级着色器节点: ${stats.advancedShaderNodes}`);
    console.log(`着色器管理节点: ${stats.shaderManagementNodes}`);
    console.log(`网络系统节点: ${stats.networkSystemNodes}`);
    console.log(`注册状态: ${stats.registered ? '已注册' : '未注册'}`);
  }
}

// 导出演示实例
export const renderingNetworkNodesDemo = new RenderingNetworkNodesDemo();

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  renderingNetworkNodesDemo.runDemo().then(() => {
    renderingNetworkNodesDemo.showRegistrationStats();
  });
}
